package exchange.common.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.CollectionUtils;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.CandlestickType;
import exchange.common.entity.Candlestick;
import exchange.common.entity.Candlestick_;
import exchange.common.entity.Symbol;
import exchange.common.predicate.CandlestickPredicate;
import exchange.spot.entity.SpotTrade;
import exchange.spot.service.SpotTradeService;

public abstract class CandlestickService<E extends Candlestick, P extends CandlestickPredicate<E>>
    extends EntityService<E, P> implements ApplicationContextAware {

  private static final int MAX_SIZE = 200;

  private static ApplicationContext APPLICATION_CONTEXT;

  @SuppressWarnings("unchecked")
  public static <E extends Candlestick, P extends CandlestickPredicate<E>>
      CandlestickService<E, P> getBean(Symbol symbol) {
    return (CandlestickService<E, P>)
        APPLICATION_CONTEXT.getBean(
            symbol.getTradeType().toLowerCamelCase()
                + "Candlestick"
                + symbol.getCurrencyPair().toUpperCamelCase()
                + "Service");
  }

  @Override
  @SuppressFBWarnings
  public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    APPLICATION_CONTEXT = applicationContext;
  }

  private String getCacheKey(Long symbolId, CandlestickType candlestickType, Date targetAt) {
    return "candlestick:" + symbolId + ":" + candlestickType + ":" + targetAt.getTime();
  }

  @Override
  protected void saveCache(E candlestick) {
    super.saveCache(candlestick);
    redisTemplate.setValue(getCacheKey(candlestick.getSymbolId()), candlestick);
  }

  @Override
  protected void deleteCache(E candlestick) {
    super.deleteCache(candlestick);
    redisTemplate.delete(getCacheKey(candlestick.getSymbolId()));
  }

  protected List<Predicate> getIndexedPredicates(
      CriteriaBuilder criteriaBuilder,
      Root<E> root,
      Long symbolId,
      CandlestickType candlestickType) {
    List<Predicate> predicates = new ArrayList<>();
    predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
    predicates.add(predicate.equalCandlestickType(criteriaBuilder, root, candlestickType));
    return predicates;
  }

  public E findOne(Long symbolId, CandlestickType candlestickType, Date targetAt) {
    E candlestick = null;

    try {
      candlestick = redisTemplate.getValue(getCacheKey(symbolId, candlestickType, targetAt));
    } catch (Exception e) {
      // do nothing
    }

    if (candlestick == null) {
      candlestick =
          customTransactionManager.find(
              getEntityClass(),
              new QueryExecutorReturner<E, E>() {
                @Override
                public E query() {
                  List<Predicate> predicates =
                      getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);
                  predicates.add(predicate.equalTargetAt(criteriaBuilder, root, targetAt));
                  return getSingleResult(entityManager, criteriaQuery, root, predicates);
                }
              });

      if (candlestick != null) {
        saveCache(candlestick);
      }
    }

    return candlestick;
  }

  public E findOrCreate(Long symbolId, CandlestickType candlestickType, Date targetAt) {
    E candlestick = findOne(symbolId, candlestickType, targetAt);

    if (candlestick == null) {
      candlestick = newEntity();
      candlestick.setProperties(symbolId, candlestickType, targetAt);
    }

    return candlestick;
  }

  public E findLatest(Long symbolId, CandlestickType candlestickType, Date targetAt) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, E>() {
          @Override
          public E query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);
            predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, targetAt));
            return getSingleResult(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  public E findOldest(Long symbolId, CandlestickType candlestickType) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, E>() {
          @Override
          public E query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);
            return getSingleResult(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.asc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  public List<E> findByCondition(
      Long symbolId, CandlestickType candlestickType, Integer count, Long since) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

            if (since != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, new Date(since)));
            }

            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.asc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  public List<E> findLatestByCondition(
      Long symbolId, CandlestickType candlestickType, Long dateFrom, Long dateTo) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

            if (dateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToTargetAt(
                      criteriaBuilder, root, new Date(dateFrom)));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, new Date(dateTo)));
            }

            List<E> result =
                getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    0,
                    MAX_SIZE,
                    criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
            Collections.reverse(result);
            return result;
          }
        });
  }

  public E findOneByCondition(
      Long symbolId,
      CandlestickType candlestickType,
      Long dateFrom,
      Long dateTo,
      boolean isAscending) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, E>() {
          @Override
          public E query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

            if (dateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToTargetAt(
                      criteriaBuilder, root, new Date(dateFrom)));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, new Date(dateTo)));
            }

            return getSingleResult(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                isAscending
                    ? criteriaBuilder.asc(root.get(Candlestick_.targetAt))
                    : criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  public List<E> findByCondition(
      Long symbolId, CandlestickType candlestickType, Date dateFrom, Date dateTo) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

            if (dateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, dateFrom));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, dateTo));
            }

            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                0,
                Integer.MAX_VALUE,
                criteriaBuilder.asc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  public List<E> findByCondition(
      Long symbolId, CandlestickType candlestickType, Date dateFrom, Date dateTo, Integer num) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

            if (dateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, dateFrom));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, dateTo));
            }

            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                0,
                num,
                criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  public void make(Symbol symbol, CandlestickType candlestickType, Date targetAt) {
    // 根据candlestickType获取目标时间
    targetAt = candlestickType.getTargetAt(targetAt);
    // 查找最新的candlestick
    E previous = findLatest(symbol.getId(), candlestickType, targetAt);

    // 如果最新的candlestick存在且未被固定
    if (previous != null && !previous.isFixed()) {
      // 查找最新的candlestick之前的candlestick
      E morePrevious =
          findPrevious(
              previous.getSymbolId(), previous.getCandlestickType(), previous.getTargetAt());

      // 如果candlestickType为PT1M，则调用makeMinute1方法
      if (candlestickType == CandlestickType.PT1M) {
        makeMinute1(symbol, previous, morePrevious);
      } else {
        // 否则调用makeWithoutMinute1方法
        makeWithoutMinute1(symbol, previous, morePrevious);
      }
    }

    // 查找或创建candlestick
    E candlestick = findOrCreate(symbol.getId(), candlestickType, targetAt);

    // 如果candlestickType为PT1M，则调用makeMinute1方法
    if (candlestickType == CandlestickType.PT1M) {
      makeMinute1(symbol, candlestick, previous);
    } else {
      // 否则调用makeWithoutMinute1方法
      makeWithoutMinute1(symbol, candlestick, previous);
    }
  }

  private E findPrevious(Long symbolId, CandlestickType candlestickType, Date targetAt) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, E>() {
          @Override
          public E query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);
            predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, targetAt));
            return getSingleResult(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  protected void updateByPrevious(E candlestick, E previous) {
    if (previous == null) {
      return;
    }

    candlestick.update(
        previous.getClose(),
        previous.getClose(),
        previous.getClose(),
        previous.getClose(),
        BigDecimal.ZERO);
  }

  private void makeMinute1(Symbol symbol, E candlestick, E previous) {
    // Takerには販売所のスプレッドが含まれるため、Makerで生成する
    List<SpotTrade> spotTrades =
        SpotTradeService.getBean(symbol)
            .findMakerByCondition(
                symbol.getId(),
                candlestick.getTargetAt(),
                candlestick.getCandlestickType().getNextTargetAt(candlestick.getTargetAt()));
    candlestick.reset();

    if (CollectionUtils.isEmpty(spotTrades)) {
      updateByPrevious(candlestick, previous);
    } else {
      spotTrades.forEach(
          cxrTradeData ->
              candlestick.update(
                  cxrTradeData.getPrice(),
                  cxrTradeData.getPrice(),
                  cxrTradeData.getPrice(),
                  cxrTradeData.getPrice(),
                  cxrTradeData.getAmount()));
    }

    if (new Date()
        .after(candlestick.getCandlestickType().getNextTargetAt(candlestick.getTargetAt()))) {
      candlestick.setFixed(true);
    }

    save(candlestick);
  }

  private void makeWithoutMinute1(Symbol symbol, E candlestick, E previous) {
    Date nextTargetAt = candlestick.getCandlestickType().getNextTargetAt(candlestick.getTargetAt());
    List<E> elements =
        findByCondition(
            candlestick.getSymbolId(),
            candlestick.getCandlestickType().getElementType(),
            candlestick.getTargetAt(),
            nextTargetAt);
    candlestick.reset();

    if (CollectionUtils.isEmpty(elements)) {
      updateByPrevious(candlestick, previous);
    } else {
      elements
          .stream()
          .forEach(
              element ->
                  candlestick.update(
                      element.getOpen(),
                      element.getHigh(),
                      element.getLow(),
                      element.getClose(),
                      element.getVolume()));
    }

    if (new Date().after(nextTargetAt)) {
      candlestick.setFixed(true);
    }

    save(candlestick);
  }

  @Override
  public void redisPublish(Candlestick entity){
    redisPublisher.publish(entity);
  }
}
